# Windows Frida 拦截 Android 微信小程序网络流量方案

本方案提供了使用 Windows 上的 Frida 来拦截 Android 设备微信小程序网络流量的完整解决方案。

## 环境要求

### Windows 端
- Python 3.7+
- Frida 工具链
- ADB (Android Debug Bridge)

### Android 设备
- Root 权限的 Android 设备
- 已安装微信应用
- 启用 USB 调试模式

## 安装步骤

### 1. Windows 端环境配置

```bash
# 安装 Python 依赖
pip install frida-tools

# 验证 ADB 连接
adb devices
```

### 2. Android 设备配置

```bash
# 下载适合设备架构的 frida-server
# 从 https://github.com/frida/frida/releases 下载

# 推送到设备
adb push frida-server /data/local/tmp/
adb shell chmod 755 /data/local/tmp/frida-server

# 启动 frida-server
adb shell "su -c '/data/local/tmp/frida-server &'"

# 设置端口转发
adb forward tcp:27042 tcp:27042
```

## 使用方法

### 方法一：使用 Python 控制脚本（推荐）

```bash
# 基本使用
python frida_wechat_controller.py

# 指定输出文件
python frida_wechat_controller.py --output wechat_traffic.json

# 指定设备ID
python frida_wechat_controller.py --device "设备ID"

# 使用自定义脚本
python frida_wechat_controller.py --script custom_hook.js
```

### 方法二：直接使用 Frida 命令

```bash
# 附加到正在运行的微信进程
frida -U -n "WeChat" -l wx_request_advanced_hook.js

# 启动微信并附加
frida -U -f com.tencent.mm -l wx_request_advanced_hook.js --no-pause

# 使用基础脚本
frida -U -f com.tencent.mm -l wechat_miniprogram_hook.js --no-pause
```

## 脚本说明

### 1. wechat_miniprogram_hook.js
- 基础网络拦截脚本
- Hook OkHttp、HttpURLConnection 等常用网络库
- 拦截 WebView 网络请求
- 适合初学者使用

### 2. wx_request_advanced_hook.js
- 高级拦截脚本
- 深度分析微信网络请求
- 提供详细的请求/响应信息
- 包含统计和导出功能

### 3. frida_wechat_controller.py
- Python 控制脚本
- 自动化设备连接和进程附加
- 数据收集和导出
- 统计分析功能

## 拦截原理

### 1. 网络库 Hook
- **OkHttp**: 微信主要使用的 HTTP 客户端
- **HttpURLConnection**: Android 原生网络库
- **WebView**: 小程序运行环境

### 2. 小程序特定拦截
- **wx.request API**: 小程序网络请求接口
- **WebView JavaScript**: 拦截 JS 层面的网络调用
- **微信内部网络类**: Hook 微信特定的网络实现

### 3. 数据捕获点
```
小程序 JS 层 (wx.request)
    ↓
WebView JavaScript Bridge
    ↓
Android Java 层 (OkHttp/HttpURLConnection)
    ↓
网络请求发送
```

## 输出数据格式

拦截的数据将包含以下信息：

```json
{
  "timestamp": "2024-01-01 12:00:00",
  "data": {
    "type": "request",
    "url": "https://api.weixin.qq.com/...",
    "method": "POST",
    "headers": {...},
    "body": "...",
    "response": {...}
  }
}
```

## 常见问题解决

### 1. 设备连接问题
```bash
# 检查设备连接
adb devices

# 重启 ADB 服务
adb kill-server
adb start-server

# 检查 frida-server 状态
adb shell ps | grep frida
```

### 2. 权限问题
```bash
# 确保设备已 Root
adb shell su -c "id"

# 检查 frida-server 权限
adb shell ls -la /data/local/tmp/frida-server
```

### 3. 微信进程问题
```bash
# 查找微信进程
frida-ps -U | grep -i wechat

# 强制重启微信
adb shell am force-stop com.tencent.mm
adb shell am start -n com.tencent.mm/.ui.LauncherUI
```

## 高级用法

### 1. 自定义过滤规则
修改脚本中的过滤条件来只拦截特定的请求：

```javascript
// 只拦截包含特定关键词的请求
if (url.indexOf("api.weixin.qq.com") !== -1) {
    console.log("拦截到微信API请求: " + url);
}
```

### 2. 实时数据分析
```python
# 在 Python 脚本中添加实时分析逻辑
def analyze_request(data):
    if "login" in data.get("url", ""):
        print("检测到登录请求!")
```

### 3. 数据导出格式
支持多种导出格式：
- JSON (默认)
- CSV
- 文本日志

## 注意事项

1. **法律合规**: 仅用于学习和研究目的，不得用于非法用途
2. **设备安全**: Root 设备存在安全风险，请谨慎操作
3. **版本兼容**: 微信版本更新可能影响 Hook 效果
4. **性能影响**: 大量拦截可能影响设备性能

## 故障排除

### 常见错误及解决方案

1. **"Failed to attach: unable to find process"**
   - 确保微信正在运行
   - 检查包名是否正确

2. **"Failed to load script"**
   - 检查脚本语法
   - 确保文件路径正确

3. **"Device not found"**
   - 检查 USB 连接
   - 重启 ADB 服务

## 扩展功能

### 1. 添加新的拦截点
```javascript
// Hook 其他网络库
var Volley = Java.use("com.android.volley.Request");
// 实现自定义拦截逻辑
```

### 2. 集成其他工具
- 与 Burp Suite 联动
- 集成到 CI/CD 流程
- 添加 Web 界面

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
