// 最小化微信网络拦截脚本
// 只Hook最基础和最安全的网络接口

console.log("[*] 启动最小化网络拦截...");

function minimalHook() {
    Java.perform(function() {
        console.log("[*] 开始最小化Hook...");

        // 尝试多种方式Hook OkHttp
        var okHttpHooked = false;

        // 方法1: 尝试Hook OkHttp3
        try {
            var OkHttpClient = Java.use("okhttp3.OkHttpClient");

            OkHttpClient.newCall.implementation = function(request) {
                var url = request.url().toString();
                console.log("[OkHttp3] 请求: " + url);

                // 记录所有请求，不只是小程序
                if (url.indexOf("http") !== -1) {
                    console.log("[OkHttp3] 完整URL: " + url);
                    console.log("[OkHttp3] 方法: " + request.method());
                }

                return this.newCall(request);
            };

            console.log("[+] OkHttp3 Hook成功");
            okHttpHooked = true;

        } catch (e) {
            console.log("[-] OkHttp3不可用: " + e);
        }

        // 方法2: 尝试Hook OkHttp的Call接口
        if (!okHttpHooked) {
            try {
                var Call = Java.use("okhttp3.Call");
                var RealCall = Java.use("okhttp3.RealCall");

                RealCall.execute.implementation = function() {
                    try {
                        var request = this.request();
                        var url = request.url().toString();
                        console.log("[RealCall] 同步请求: " + url);
                    } catch (e) {
                        console.log("[RealCall] 获取请求信息失败");
                    }
                    return this.execute();
                };

                console.log("[+] OkHttp RealCall Hook成功");
                okHttpHooked = true;

            } catch (e) {
                console.log("[-] OkHttp RealCall不可用: " + e);
            }
        }

        // 方法3: Hook OkHttp的Interceptor
        if (!okHttpHooked) {
            try {
                var Interceptor = Java.use("okhttp3.Interceptor");
                console.log("[+] 找到OkHttp Interceptor接口");
                okHttpHooked = true;
            } catch (e) {
                console.log("[-] OkHttp Interceptor不可用: " + e);
            }
        }

        try {
            // 2. Hook HttpURLConnection - Android标准库
            var HttpURLConnection = Java.use("java.net.HttpURLConnection");

            // Hook多个方法来确保捕获
            HttpURLConnection.connect.implementation = function() {
                var url = this.getURL().toString();
                console.log("[HttpURLConnection] 连接: " + url);
                return this.connect();
            };

            HttpURLConnection.getInputStream.implementation = function() {
                var url = this.getURL().toString();
                console.log("[HttpURLConnection] 获取输入流: " + url);
                return this.getInputStream();
            };

            console.log("[+] HttpURLConnection Hook成功");

        } catch (e) {
            console.log("[-] HttpURLConnection不可用: " + e);
        }

        try {
            // 3. Hook WebView - 小程序运行环境
            var WebView = Java.use("android.webkit.WebView");

            // Hook loadUrl方法
            WebView.loadUrl.overload("java.lang.String").implementation = function(url) {
                console.log("[WebView] 加载URL: " + url);
                return this.loadUrl(url);
            };

            // Hook loadUrl with headers
            WebView.loadUrl.overload("java.lang.String", "java.util.Map").implementation = function(url, headers) {
                console.log("[WebView] 加载URL(带头): " + url);
                return this.loadUrl(url, headers);
            };

            // Hook evaluateJavascript
            WebView.evaluateJavascript.implementation = function(script, callback) {
                if (script && script.length > 0) {
                    if (script.indexOf("request") !== -1 || script.indexOf("http") !== -1) {
                        console.log("[WebView] JS网络相关: " + script.substring(0, 100) + "...");
                    }
                }
                return this.evaluateJavascript(script, callback);
            };

            console.log("[+] WebView Hook成功");

        } catch (e) {
            console.log("[-] WebView不可用: " + e);
        }

        // 4. Hook Socket连接 (备用方案)
        try {
            var Socket = Java.use("java.net.Socket");
            Socket.connect.overload("java.net.SocketAddress").implementation = function(endpoint) {
                console.log("[Socket] 连接到: " + endpoint.toString());
                return this.connect(endpoint);
            };
            console.log("[+] Socket Hook成功");
        } catch (e) {
            console.log("[-] Socket不可用: " + e);
        }

        // 5. 尝试Hook微信可能使用的其他网络库
        try {
            // Apache HttpClient
            var DefaultHttpClient = Java.use("org.apache.http.impl.client.DefaultHttpClient");
            console.log("[+] 找到Apache HttpClient");
        } catch (e) {
            console.log("[-] Apache HttpClient不可用");
        }

        console.log("[+] 最小化Hook完成");
        console.log("[*] 现在请操作微信，查看网络请求...");
    });
}

// 延迟启动，避免过早Hook
setTimeout(minimalHook, 2000);

console.log("[*] 最小化脚本加载完成");
