// 最小化微信网络拦截脚本
// 只Hook最基础和最安全的网络接口

console.log("[*] 启动最小化网络拦截...");

function minimalHook() {
    Java.perform(function() {
        console.log("[*] 开始最小化Hook...");
        
        // 只Hook最基础的网络库
        try {
            // 1. Hook OkHttp - 最常用且相对安全
            var OkHttpClient = Java.use("okhttp3.OkHttpClient");
            
            OkHttpClient.newCall.implementation = function(request) {
                var url = request.url().toString();
                
                // 只打印小程序相关的URL
                if (url.indexOf("servicewechat.com") !== -1 || 
                    url.indexOf("weixin.qq.com") !== -1) {
                    console.log("[OkHttp] 小程序请求: " + url);
                }
                
                return this.newCall(request);
            };
            
            console.log("[+] OkHttp Hook成功");
            
        } catch (e) {
            console.log("[-] OkHttp不可用");
        }
        
        try {
            // 2. Hook HttpURLConnection - Android标准库
            var HttpURLConnection = Java.use("java.net.HttpURLConnection");
            
            HttpURLConnection.connect.implementation = function() {
                var url = this.getURL().toString();
                
                if (url.indexOf("servicewechat.com") !== -1 || 
                    url.indexOf("weixin.qq.com") !== -1) {
                    console.log("[HttpURLConnection] 小程序请求: " + url);
                }
                
                return this.connect();
            };
            
            console.log("[+] HttpURLConnection Hook成功");
            
        } catch (e) {
            console.log("[-] HttpURLConnection不可用");
        }
        
        try {
            // 3. Hook WebView loadUrl - 小程序页面加载
            var WebView = Java.use("android.webkit.WebView");
            
            WebView.loadUrl.overload("java.lang.String").implementation = function(url) {
                if (url.indexOf("__wxapp") !== -1 || 
                    url.indexOf("servicewechat.com") !== -1) {
                    console.log("[WebView] 小程序页面: " + url);
                }
                
                return this.loadUrl(url);
            };
            
            console.log("[+] WebView Hook成功");
            
        } catch (e) {
            console.log("[-] WebView不可用");
        }
        
        console.log("[+] 最小化Hook完成");
    });
}

// 延迟启动，避免过早Hook
setTimeout(minimalHook, 2000);

console.log("[*] 最小化脚本加载完成");
