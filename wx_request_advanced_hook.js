// 高级微信小程序wx.request拦截脚本
// 专门针对微信小程序的网络请求进行深度拦截和分析

console.log("[*] 启动高级微信小程序网络拦截...");

// 存储拦截到的请求数据
var interceptedRequests = [];
var requestCounter = 0;

function hookWxRequestAdvanced() {
    Java.perform(function() {
        try {
            // Hook微信WebView相关类
            var WebViewClient = Java.use("android.webkit.WebViewClient");
            WebViewClient.shouldInterceptRequest.overload("android.webkit.WebView", "android.webkit.WebResourceRequest").implementation = function(view, request) {
                var url = request.getUrl().toString();
                var method = request.getMethod();
                var headers = request.getRequestHeaders();
                
                console.log("\n[WebViewClient] ===================");
                console.log("[WebViewClient] URL: " + url);
                console.log("[WebViewClient] Method: " + method);
                console.log("[WebViewClient] Headers: " + JSON.stringify(headers));
                
                // 检查是否是小程序相关请求
                if (url.indexOf("servicewechat.com") !== -1 || 
                    url.indexOf("weixin.qq.com") !== -1 ||
                    url.indexOf("wx.qlogo.cn") !== -1) {
                    console.log("[MiniProgram] 检测到小程序相关请求!");
                    
                    // 保存请求信息
                    var requestInfo = {
                        id: ++requestCounter,
                        timestamp: new Date().toISOString(),
                        url: url,
                        method: method,
                        headers: headers,
                        type: "miniprogram"
                    };
                    interceptedRequests.push(requestInfo);
                }
                
                return this.shouldInterceptRequest(view, request);
            };

            // Hook X5WebView (腾讯X5内核)
            try {
                var X5WebView = Java.use("com.tencent.smtt.sdk.WebView");
                X5WebView.loadUrl.overload("java.lang.String").implementation = function(url) {
                    console.log("[X5WebView] Loading: " + url);
                    if (url.indexOf("__wxappservice__") !== -1 || url.indexOf("__wxapp__") !== -1) {
                        console.log("[X5WebView] 检测到小程序服务层请求!");
                    }
                    return this.loadUrl(url);
                };
            } catch (e) {
                console.log("[-] X5WebView not found or hook failed");
            }

            // Hook网络库 - 更详细的OkHttp拦截
            try {
                var Interceptor = Java.use("okhttp3.Interceptor");
                var Response = Java.use("okhttp3.Response");
                var ResponseBody = Java.use("okhttp3.ResponseBody");
                var Buffer = Java.use("okio.Buffer");
                
                // 创建自定义拦截器
                var CustomInterceptor = Java.registerClass({
                    name: "com.custom.NetworkInterceptor",
                    implements: [Interceptor],
                    methods: {
                        intercept: function(chain) {
                            var request = chain.request();
                            var url = request.url().toString();
                            
                            console.log("\n[CustomInterceptor] ===================");
                            console.log("[CustomInterceptor] Request URL: " + url);
                            console.log("[CustomInterceptor] Method: " + request.method());
                            
                            // 打印请求体
                            var requestBody = request.body();
                            if (requestBody != null) {
                                try {
                                    var buffer = Java.use("okio.Buffer").$new();
                                    requestBody.writeTo(buffer);
                                    var bodyString = buffer.readUtf8();
                                    console.log("[CustomInterceptor] Request Body: " + bodyString);
                                } catch (e) {
                                    console.log("[CustomInterceptor] Could not read request body: " + e);
                                }
                            }
                            
                            // 执行请求
                            var response = chain.proceed(request);
                            
                            // 打印响应
                            console.log("[CustomInterceptor] Response Code: " + response.code());
                            
                            try {
                                var responseBody = response.body();
                                var source = responseBody.source();
                                source.request(Java.use("java.lang.Long").MAX_VALUE);
                                var buffer = source.buffer();
                                var responseString = buffer.clone().readUtf8();
                                console.log("[CustomInterceptor] Response Body: " + responseString);
                                
                                // 检查是否是小程序API响应
                                if (responseString.indexOf("errcode") !== -1 || 
                                    responseString.indexOf("openid") !== -1 ||
                                    responseString.indexOf("session_key") !== -1) {
                                    console.log("[MiniProgram API] 检测到小程序API响应!");
                                }
                            } catch (e) {
                                console.log("[CustomInterceptor] Could not read response body: " + e);
                            }
                            
                            return response;
                        }
                    }
                });
                
            } catch (e) {
                console.log("[-] Custom interceptor creation failed: " + e);
            }

            // Hook微信特定的网络请求类
            try {
                // 尝试Hook微信的网络请求管理器
                var classes = Java.enumerateLoadedClassesSync();
                var wechatNetworkClasses = classes.filter(function(className) {
                    return className.indexOf("com.tencent.mm") !== -1 && 
                           (className.indexOf("network") !== -1 || 
                            className.indexOf("http") !== -1 ||
                            className.indexOf("api") !== -1);
                });
                
                console.log("[*] 找到微信网络相关类: " + wechatNetworkClasses.length + " 个");
                
                wechatNetworkClasses.forEach(function(className) {
                    try {
                        var clazz = Java.use(className);
                        console.log("[*] 分析类: " + className);
                        
                        // 获取所有方法
                        var methods = clazz.class.getDeclaredMethods();
                        methods.forEach(function(method) {
                            var methodName = method.getName();
                            if (methodName.indexOf("request") !== -1 || 
                                methodName.indexOf("send") !== -1 ||
                                methodName.indexOf("post") !== -1 ||
                                methodName.indexOf("get") !== -1 ||
                                methodName.indexOf("doInBackground") !== -1) {
                                console.log("[*] 找到可能的请求方法: " + methodName);
                            }
                        });
                    } catch (e) {
                        // 忽略无法访问的类
                    }
                });
                
            } catch (e) {
                console.log("[-] 微信类分析失败: " + e);
            }

        } catch (e) {
            console.log("[-] 高级Hook设置失败: " + e);
        }
    });
}

// 定期输出拦截统计
function startReportTimer() {
    setInterval(function() {
        if (interceptedRequests.length > 0) {
            console.log("\n[统计] 已拦截请求数量: " + interceptedRequests.length);
            console.log("[统计] 最近5个请求:");
            var recent = interceptedRequests.slice(-5);
            recent.forEach(function(req) {
                console.log("  - [" + req.id + "] " + req.method + " " + req.url);
            });
        }
    }, 30000); // 每30秒输出一次统计
}

// 导出拦截数据的函数
function exportInterceptedData() {
    console.log("\n[导出] 所有拦截的请求数据:");
    console.log(JSON.stringify(interceptedRequests, null, 2));
}

// 启动Hook
setTimeout(function() {
    hookWxRequestAdvanced();
    startReportTimer();
    
    // 注册全局函数供外部调用
    global.exportData = exportInterceptedData;
    global.getRequestCount = function() { return interceptedRequests.length; };
    global.clearRequests = function() { interceptedRequests = []; requestCounter = 0; };
    
    console.log("[*] 高级拦截脚本启动完成!");
    console.log("[*] 可用命令:");
    console.log("  - exportData() : 导出所有拦截数据");
    console.log("  - getRequestCount() : 获取拦截请求数量");
    console.log("  - clearRequests() : 清空拦截数据");
    
}, 2000);
