// 微信网络库发现脚本
// 用于发现微信实际使用的网络库和方法

console.log("[*] 启动微信网络库发现脚本...");

function discoverNetworkLibraries() {
    // 等待Java环境准备就绪
    Java.perform(function() {
        console.log("[*] Java环境已就绪，开始发现网络库...");
        
        // 1. 检查常见的网络库是否存在
        var networkLibraries = [
            "okhttp3.OkHttpClient",
            "okhttp3.RealCall", 
            "okhttp3.Call",
            "okhttp.OkHttpClient",  // OkHttp 2.x
            "com.squareup.okhttp.OkHttpClient",
            "org.apache.http.impl.client.DefaultHttpClient",
            "java.net.HttpURLConnection",
            "javax.net.ssl.HttpsURLConnection",
            "android.webkit.WebView"
        ];
        
        networkLibraries.forEach(function(libName) {
            try {
                var lib = Java.use(libName);
                console.log("[+] 发现网络库: " + libName);
                
                // 尝试列出一些关键方法
                if (libName.indexOf("OkHttp") !== -1) {
                    try {
                        var methods = lib.class.getDeclaredMethods();
                        console.log("  方法数量: " + methods.length);
                        for (var i = 0; i < Math.min(5, methods.length); i++) {
                            console.log("  - " + methods[i].getName());
                        }
                    } catch (e) {
                        console.log("  无法获取方法列表");
                    }
                }
            } catch (e) {
                console.log("[-] 网络库不存在: " + libName);
            }
        });
        
        // 2. 搜索微信特定的网络类
        console.log("\n[*] 搜索微信网络相关类...");
        
        setTimeout(function() {
            try {
                Java.enumerateLoadedClasses({
                    onMatch: function(className) {
                        // 查找可能的网络相关类
                        if ((className.indexOf("com.tencent.mm") !== -1 || 
                             className.indexOf("com.tencent.mars") !== -1) &&
                            (className.indexOf("network") !== -1 || 
                             className.indexOf("http") !== -1 ||
                             className.indexOf("request") !== -1 ||
                             className.indexOf("Net") !== -1)) {
                            console.log("[发现] 微信网络类: " + className);
                        }
                        
                        // 查找OkHttp相关类
                        if (className.indexOf("okhttp") !== -1) {
                            console.log("[发现] OkHttp类: " + className);
                        }
                    },
                    onComplete: function() {
                        console.log("[*] 类枚举完成");
                    }
                });
            } catch (e) {
                console.log("[-] 类枚举失败: " + e);
            }
        }, 3000);
        
        // 3. Hook所有可能的网络入口点
        hookAllNetworkEntries();
    });
}

function hookAllNetworkEntries() {
    console.log("\n[*] Hook所有网络入口点...");
    
    // Hook URL类 - 最底层的URL处理
    try {
        var URL = Java.use("java.net.URL");
        URL.openConnection.overload().implementation = function() {
            console.log("[URL] 打开连接: " + this.toString());
            return this.openConnection();
        };
        console.log("[+] URL Hook成功");
    } catch (e) {
        console.log("[-] URL Hook失败: " + e);
    }
    
    // Hook URLConnection
    try {
        var URLConnection = Java.use("java.net.URLConnection");
        URLConnection.connect.implementation = function() {
            console.log("[URLConnection] 连接: " + this.getURL().toString());
            return this.connect();
        };
        console.log("[+] URLConnection Hook成功");
    } catch (e) {
        console.log("[-] URLConnection Hook失败: " + e);
    }
    
    // Hook InputStream - 捕获数据读取
    try {
        var InputStream = Java.use("java.io.InputStream");
        InputStream.read.overload().implementation = function() {
            // 只在第一次读取时打印，避免日志过多
            if (!this._logged) {
                console.log("[InputStream] 开始读取数据流");
                this._logged = true;
            }
            return this.read();
        };
        console.log("[+] InputStream Hook成功");
    } catch (e) {
        console.log("[-] InputStream Hook失败: " + e);
    }
    
    // Hook OutputStream - 捕获数据发送
    try {
        var OutputStream = Java.use("java.io.OutputStream");
        OutputStream.write.overload("[B").implementation = function(data) {
            if (data && data.length > 0) {
                console.log("[OutputStream] 发送数据: " + data.length + " 字节");
                // 尝试解析为字符串
                try {
                    var str = Java.use("java.lang.String").$new(data);
                    if (str.indexOf("http") !== -1 || str.indexOf("POST") !== -1 || str.indexOf("GET") !== -1) {
                        console.log("[OutputStream] HTTP数据: " + str.substring(0, 200));
                    }
                } catch (e) {
                    // 忽略非文本数据
                }
            }
            return this.write(data);
        };
        console.log("[+] OutputStream Hook成功");
    } catch (e) {
        console.log("[-] OutputStream Hook失败: " + e);
    }
    
    // Hook DNS解析
    try {
        var InetAddress = Java.use("java.net.InetAddress");
        InetAddress.getByName.implementation = function(host) {
            console.log("[DNS] 解析域名: " + host);
            return this.getByName(host);
        };
        console.log("[+] DNS Hook成功");
    } catch (e) {
        console.log("[-] DNS Hook失败: " + e);
    }
    
    // Hook SSL连接
    try {
        var HttpsURLConnection = Java.use("javax.net.ssl.HttpsURLConnection");
        HttpsURLConnection.connect.implementation = function() {
            console.log("[HTTPS] SSL连接: " + this.getURL().toString());
            return this.connect();
        };
        console.log("[+] HTTPS Hook成功");
    } catch (e) {
        console.log("[-] HTTPS Hook失败: " + e);
    }
}

// 监控线程创建 - 网络请求通常在后台线程
function monitorThreads() {
    try {
        var Thread = Java.use("java.lang.Thread");
        Thread.$init.overload("java.lang.Runnable", "java.lang.String").implementation = function(runnable, name) {
            if (name && (name.indexOf("http") !== -1 || 
                        name.indexOf("network") !== -1 || 
                        name.indexOf("Net") !== -1)) {
                console.log("[Thread] 网络相关线程: " + name);
            }
            return this.$init(runnable, name);
        };
        console.log("[+] Thread监控启动");
    } catch (e) {
        console.log("[-] Thread监控失败: " + e);
    }
}

// 启动发现流程 - 增加延迟确保Java环境就绪
setTimeout(function() {
    console.log("[*] 开始初始化...");
    try {
        discoverNetworkLibraries();
        monitorThreads();

        console.log("\n[*] 网络库发现完成");
        console.log("[*] 现在请操作微信，观察网络活动...");
        console.log("[*] 特别注意打开小程序时的日志输出");
    } catch (e) {
        console.log("[-] 初始化失败: " + e);
        console.log("[*] 3秒后重试...");
        setTimeout(function() {
            discoverNetworkLibraries();
        }, 3000);
    }
}, 3000); // 增加到3秒延迟

console.log("[*] 发现脚本加载完成");
