// 安全版微信小程序网络请求拦截脚本
// 避免激进的类枚举，采用更温和的Hook策略

console.log("[*] 启动安全版微信小程序网络拦截...");

var interceptedRequests = [];
var requestCounter = 0;

function safeHook() {
    Java.perform(function() {
        console.log("[*] 开始安全Hook...");
        
        try {
            // 1. Hook标准网络库 - OkHttp (最常用且相对安全)
            hookOkHttp();
        } catch (e) {
            console.log("[-] OkHttp hook失败: " + e);
        }
        
        try {
            // 2. Hook HttpURLConnection (Android标准库)
            hookHttpURLConnection();
        } catch (e) {
            console.log("[-] HttpURLConnection hook失败: " + e);
        }
        
        try {
            // 3. Hook WebView (小程序运行环境)
            hookWebView();
        } catch (e) {
            console.log("[-] WebView hook失败: " + e);
        }
        
        // 延迟执行更敏感的Hook
        setTimeout(function() {
            try {
                hookWeChatSpecific();
            } catch (e) {
                console.log("[-] 微信特定Hook失败: " + e);
            }
        }, 5000);
    });
}

function hookOkHttp() {
    try {
        var OkHttpClient = Java.use("okhttp3.OkHttpClient");
        console.log("[+] 找到OkHttpClient");
        
        // Hook newCall方法
        OkHttpClient.newCall.implementation = function(request) {
            try {
                var url = request.url().toString();
                var method = request.method();
                
                // 只记录关键信息，避免过度访问
                if (isInterestingUrl(url)) {
                    console.log("\n[OkHttp] ===================");
                    console.log("[OkHttp] URL: " + url);
                    console.log("[OkHttp] Method: " + method);
                    
                    // 安全地获取请求头
                    try {
                        var headers = request.headers();
                        var headerNames = headers.names();
                        console.log("[OkHttp] Headers count: " + headerNames.size());
                    } catch (e) {
                        console.log("[OkHttp] Headers access failed");
                    }
                    
                    recordRequest(url, method, "OkHttp");
                }
            } catch (e) {
                console.log("[-] OkHttp处理错误: " + e);
            }
            
            return this.newCall(request);
        };
        
        console.log("[+] OkHttp Hook成功");
    } catch (e) {
        console.log("[-] OkHttp类未找到: " + e);
    }
}

function hookHttpURLConnection() {
    try {
        var HttpURLConnection = Java.use("java.net.HttpURLConnection");
        console.log("[+] 找到HttpURLConnection");
        
        HttpURLConnection.getResponseCode.implementation = function() {
            try {
                var url = this.getURL().toString();
                var method = this.getRequestMethod();
                
                if (isInterestingUrl(url)) {
                    console.log("\n[HttpURLConnection] ===================");
                    console.log("[HttpURLConnection] URL: " + url);
                    console.log("[HttpURLConnection] Method: " + method);
                    
                    recordRequest(url, method, "HttpURLConnection");
                }
            } catch (e) {
                console.log("[-] HttpURLConnection处理错误: " + e);
            }
            
            var result = this.getResponseCode();
            
            if (isInterestingUrl(this.getURL().toString())) {
                console.log("[HttpURLConnection] Response Code: " + result);
            }
            
            return result;
        };
        
        console.log("[+] HttpURLConnection Hook成功");
    } catch (e) {
        console.log("[-] HttpURLConnection Hook失败: " + e);
    }
}

function hookWebView() {
    try {
        var WebView = Java.use("android.webkit.WebView");
        console.log("[+] 找到WebView");
        
        // Hook loadUrl方法
        WebView.loadUrl.overload("java.lang.String").implementation = function(url) {
            if (isInterestingUrl(url)) {
                console.log("\n[WebView] ===================");
                console.log("[WebView] Loading URL: " + url);
                recordRequest(url, "GET", "WebView");
            }
            return this.loadUrl(url);
        };
        
        // Hook evaluateJavascript (小程序JS执行)
        WebView.evaluateJavascript.implementation = function(script, callback) {
            if (script && (script.indexOf("wx.request") !== -1 || 
                          script.indexOf("XMLHttpRequest") !== -1)) {
                console.log("\n[WebView JS] ===================");
                console.log("[WebView JS] 检测到网络相关JS: " + script.substring(0, 200) + "...");
            }
            return this.evaluateJavascript(script, callback);
        };
        
        console.log("[+] WebView Hook成功");
    } catch (e) {
        console.log("[-] WebView Hook失败: " + e);
    }
}

function hookWeChatSpecific() {
    console.log("[*] 开始微信特定Hook (安全模式)...");
    
    try {
        // 只Hook已知安全的微信网络类
        var safeClasses = [
            "com.tencent.mm.network.Request",
            "com.tencent.mm.protocal.protobuf.BaseRequest",
            "com.tencent.mm.plugin.appbrand.networking.AppBrandNetworking"
        ];
        
        safeClasses.forEach(function(className) {
            try {
                var clazz = Java.use(className);
                console.log("[+] 成功Hook微信类: " + className);
                
                // 只Hook构造函数，相对安全
                var constructors = clazz.class.getDeclaredConstructors();
                console.log("[*] " + className + " 有 " + constructors.length + " 个构造函数");
                
            } catch (e) {
                console.log("[-] 微信类不存在或无法访问: " + className);
            }
        });
        
    } catch (e) {
        console.log("[-] 微信特定Hook失败: " + e);
    }
}

function isInterestingUrl(url) {
    if (!url) return false;
    
    var interestingPatterns = [
        "servicewechat.com",
        "weixin.qq.com",
        "wx.qlogo.cn",
        "api.weixin.qq.com",
        "mp.weixin.qq.com",
        "wxapp.tc.qq.com"
    ];
    
    return interestingPatterns.some(function(pattern) {
        return url.indexOf(pattern) !== -1;
    });
}

function recordRequest(url, method, source) {
    var record = {
        id: ++requestCounter,
        timestamp: new Date().toISOString(),
        url: url,
        method: method,
        source: source
    };
    
    interceptedRequests.push(record);
    
    // 限制内存使用，只保留最近1000条记录
    if (interceptedRequests.length > 1000) {
        interceptedRequests = interceptedRequests.slice(-1000);
    }
}

// 定期输出统计信息
function startSafeReporting() {
    setInterval(function() {
        if (interceptedRequests.length > 0) {
            console.log("\n[统计] 已拦截请求: " + interceptedRequests.length + " 条");
            
            // 显示最近的请求
            var recent = interceptedRequests.slice(-3);
            recent.forEach(function(req) {
                console.log("  [" + req.source + "] " + req.method + " " + req.url);
            });
        }
    }, 30000); // 每30秒报告一次
}

// 导出函数
function exportSafeData() {
    console.log("\n[导出] 拦截数据:");
    console.log(JSON.stringify(interceptedRequests.slice(-50), null, 2)); // 只导出最近50条
}

// 清理函数
function cleanup() {
    console.log("[*] 清理资源...");
    interceptedRequests = [];
    requestCounter = 0;
}

// 启动脚本
setTimeout(function() {
    safeHook();
    startSafeReporting();
    
    // 注册全局函数
    global.exportData = exportSafeData;
    global.getCount = function() { return interceptedRequests.length; };
    global.cleanup = cleanup;
    
    console.log("[+] 安全Hook脚本启动完成!");
    console.log("[*] 可用命令: exportData(), getCount(), cleanup()");
    
}, 1000);

// 异常处理
Java.perform(function() {
    Java.deoptimizeEverything();
});

console.log("[*] 脚本加载完成，使用安全模式运行...");
