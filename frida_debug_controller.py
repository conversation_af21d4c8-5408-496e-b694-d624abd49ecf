#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信网络调试控制器
专门用于调试和发现微信网络请求问题
"""

import frida
import sys
import time
import argparse

def main():
    parser = argparse.ArgumentParser(description='微信网络调试工具')
    parser.add_argument('--mode', '-m', choices=['minimal', 'discovery', 'safe'], 
                       default='discovery', help='运行模式')
    parser.add_argument('--package', '-p', default='com.tencent.mm', help='微信包名')
    
    args = parser.parse_args()
    
    # 选择脚本
    script_map = {
        'minimal': 'wx_request_minimal_hook.js',
        'discovery': 'wx_network_discovery.js', 
        'safe': 'wx_request_safe_hook.js'
    }
    
    script_file = script_map[args.mode]
    print(f"[*] 使用脚本: {script_file}")
    
    try:
        # 连接设备
        device = frida.get_usb_device()
        print(f"[+] 已连接设备: {device}")
        
        # 附加到微信
        try:
            session = device.attach(args.package)
            print(f"[+] 已附加到微信进程")
        except frida.ProcessNotFoundError:
            print(f"[*] 微信未运行，启动微信...")
            pid = device.spawn([args.package])
            session = device.attach(pid)
            device.resume(pid)
            print(f"[+] 已启动并附加到微信")
        
        # 加载脚本
        with open(script_file, 'r', encoding='utf-8') as f:
            script_code = f.read()
        
        script = session.create_script(script_code)
        
        def on_message(message, data):
            if message['type'] == 'send':
                print(f"[JS] {message['payload']}")
            elif message['type'] == 'error':
                print(f"[ERROR] {message['stack']}")
        
        script.on('message', on_message)
        script.load()
        
        print(f"[+] 脚本已加载")
        print(f"[*] 现在请操作微信，特别是打开小程序")
        print(f"[*] 按 Ctrl+C 停止")
        
        # 保持运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n[*] 停止监控...")
            script.unload()
            session.detach()
            
    except Exception as e:
        print(f"[-] 错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
