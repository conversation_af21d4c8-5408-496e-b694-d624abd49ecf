#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信小程序网络流量拦截控制器
使用Frida在Windows上拦截Android设备微信小程序的网络请求
"""

import frida
import sys
import time
import json
import argparse
from datetime import datetime

class WeChatMiniProgramInterceptor:
    def __init__(self, device_id=None, package_name="com.tencent.mm"):
        """
        初始化拦截器
        :param device_id: 设备ID，None表示USB设备
        :param package_name: 微信包名
        """
        self.device_id = device_id
        self.package_name = package_name
        self.session = None
        self.script = None
        self.intercepted_data = []
        
    def connect_device(self):
        """连接到Android设备"""
        try:
            if self.device_id:
                self.device = frida.get_device(self.device_id)
            else:
                self.device = frida.get_usb_device()
            print(f"[+] 已连接到设备: {self.device}")
            return True
        except Exception as e:
            print(f"[-] 设备连接失败: {e}")
            return False
    
    def attach_to_wechat(self):
        """附加到微信进程"""
        try:
            # 尝试附加到正在运行的微信进程
            self.session = self.device.attach(self.package_name)
            print(f"[+] 已附加到微信进程: {self.package_name}")
            return True
        except frida.ProcessNotFoundError:
            print(f"[-] 微信进程未找到，尝试启动微信...")
            try:
                # 启动微信应用
                pid = self.device.spawn([self.package_name])
                self.session = self.device.attach(pid)
                self.device.resume(pid)
                print(f"[+] 已启动并附加到微信进程: {self.package_name}")
                return True
            except Exception as e:
                print(f"[-] 启动微信失败: {e}")
                return False
        except Exception as e:
            print(f"[-] 附加到微信进程失败: {e}")
            return False
    
    def load_hook_script(self, script_path="wx_request_advanced_hook.js"):
        """加载Hook脚本"""
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                script_code = f.read()
            
            self.script = self.session.create_script(script_code)
            self.script.on('message', self.on_message)
            self.script.load()
            print(f"[+] Hook脚本已加载: {script_path}")
            return True
        except Exception as e:
            print(f"[-] 脚本加载失败: {e}")
            return False
    
    def on_message(self, message, data):
        """处理来自Frida脚本的消息"""
        if message['type'] == 'send':
            payload = message['payload']
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 保存拦截数据
            intercept_record = {
                'timestamp': timestamp,
                'data': payload
            }
            self.intercepted_data.append(intercept_record)
            
            # 打印到控制台
            print(f"[{timestamp}] {payload}")
        elif message['type'] == 'error':
            print(f"[ERROR] {message['stack']}")
    
    def start_interception(self):
        """开始拦截"""
        print("[*] 开始拦截微信小程序网络请求...")
        print("[*] 按 Ctrl+C 停止拦截")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n[*] 停止拦截...")
            self.stop_interception()
    
    def stop_interception(self):
        """停止拦截"""
        if self.script:
            self.script.unload()
        if self.session:
            self.session.detach()
        print("[+] 拦截已停止")
    
    def export_data(self, output_file=None):
        """导出拦截数据"""
        if not output_file:
            output_file = f"wechat_intercept_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.intercepted_data, f, ensure_ascii=False, indent=2)
            print(f"[+] 数据已导出到: {output_file}")
            print(f"[+] 总共拦截了 {len(self.intercepted_data)} 条记录")
        except Exception as e:
            print(f"[-] 数据导出失败: {e}")
    
    def show_statistics(self):
        """显示拦截统计"""
        total_requests = len(self.intercepted_data)
        print(f"\n[统计信息]")
        print(f"总拦截请求数: {total_requests}")
        
        if total_requests > 0:
            print(f"最早记录: {self.intercepted_data[0]['timestamp']}")
            print(f"最新记录: {self.intercepted_data[-1]['timestamp']}")
            
            # 分析URL域名
            domains = {}
            for record in self.intercepted_data:
                data = record.get('data', '')
                if 'URL:' in str(data):
                    # 简单的域名提取
                    try:
                        url_part = str(data).split('URL:')[1].split()[0]
                        if '://' in url_part:
                            domain = url_part.split('://')[1].split('/')[0]
                            domains[domain] = domains.get(domain, 0) + 1
                    except:
                        pass
            
            if domains:
                print("\n[域名统计]")
                for domain, count in sorted(domains.items(), key=lambda x: x[1], reverse=True)[:10]:
                    print(f"  {domain}: {count} 次")

def main():
    parser = argparse.ArgumentParser(description='微信小程序网络流量拦截器')
    parser.add_argument('--device', '-d', help='设备ID (默认使用USB设备)')
    parser.add_argument('--package', '-p', default='com.tencent.mm', help='微信包名')
    parser.add_argument('--script', '-s', default='wx_request_advanced_hook.js', help='Hook脚本路径')
    parser.add_argument('--output', '-o', help='输出文件路径')
    
    args = parser.parse_args()
    
    # 创建拦截器实例
    interceptor = WeChatMiniProgramInterceptor(args.device, args.package)
    
    # 连接设备
    if not interceptor.connect_device():
        sys.exit(1)
    
    # 附加到微信进程
    if not interceptor.attach_to_wechat():
        sys.exit(1)
    
    # 加载Hook脚本
    if not interceptor.load_hook_script(args.script):
        sys.exit(1)
    
    try:
        # 开始拦截
        interceptor.start_interception()
    finally:
        # 显示统计信息
        interceptor.show_statistics()
        
        # 导出数据
        if interceptor.intercepted_data:
            interceptor.export_data(args.output)

if __name__ == "__main__":
    main()
