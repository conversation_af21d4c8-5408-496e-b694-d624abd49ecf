# 微信Frida Hook故障排除指南

## 问题：Process crashed: Bad access due to invalid address

### 问题分析
这个错误表明Frida脚本在访问内存时发生了违规，通常由以下原因引起：

1. **过度的类枚举**: 批量枚举微信类触发了反调试机制
2. **内存保护**: 微信新版本加强了内存保护
3. **反射操作过于激进**: 大量的getDeclaredMethods()调用
4. **时机问题**: Hook时机过早，类还未完全加载

### 解决方案

#### 方案1: 使用安全版脚本
```bash
# 使用更安全的Hook脚本
frida -U -f com.tencent.mm -l wx_request_safe_hook.js --no-pause
```

#### 方案2: 使用最小化脚本
```bash
# 使用最基础的Hook脚本
frida -U -f com.tencent.mm -l wx_request_minimal_hook.js --no-pause
```

#### 方案3: 分步骤Hook
```javascript
// 不要一次性Hook所有内容，分步骤进行
setTimeout(function() {
    // 第一步：Hook基础网络库
    hookBasicNetworking();
}, 2000);

setTimeout(function() {
    // 第二步：Hook WebView
    hookWebView();
}, 5000);

setTimeout(function() {
    // 第三步：Hook微信特定类（如果需要）
    hookWeChatSpecific();
}, 10000);
```

## 常见错误及解决方案

### 1. "Failed to attach: unable to find process"

**原因**: 微信进程未运行或包名错误

**解决方案**:
```bash
# 检查微信进程
frida-ps -U | grep -i wechat

# 如果没有找到，手动启动微信
adb shell am start -n com.tencent.mm/.ui.LauncherUI

# 使用正确的包名
frida -U -f com.tencent.mm -l script.js --no-pause
```

### 2. "Script error: ReferenceError: Java is not defined"

**原因**: 脚本在Java环境初始化前执行

**解决方案**:
```javascript
// 确保在Java.perform中执行所有Java相关代码
Java.perform(function() {
    // 所有Hook代码放在这里
});
```

### 3. "ClassNotFoundException"

**原因**: 尝试Hook的类不存在或未加载

**解决方案**:
```javascript
// 使用try-catch包装
try {
    var SomeClass = Java.use("com.example.SomeClass");
    // Hook代码
} catch (e) {
    console.log("类不存在: " + e);
}

// 或者先检查类是否存在
Java.enumerateLoadedClasses({
    onMatch: function(className) {
        if (className.indexOf("target") !== -1) {
            console.log("找到目标类: " + className);
        }
    },
    onComplete: function() {}
});
```

### 4. "Device not found"

**原因**: 设备连接问题

**解决方案**:
```bash
# 检查设备连接
adb devices

# 重启ADB
adb kill-server
adb start-server

# 检查frida-server
adb shell ps | grep frida

# 重启frida-server
adb shell "su -c 'killall frida-server'"
adb shell "su -c '/data/local/tmp/frida-server &'"
```

### 5. "Permission denied"

**原因**: 权限不足

**解决方案**:
```bash
# 确保设备已Root
adb shell su -c "id"

# 检查frida-server权限
adb shell ls -la /data/local/tmp/frida-server

# 重新设置权限
adb shell chmod 755 /data/local/tmp/frida-server
```

## 最佳实践

### 1. 渐进式Hook策略
```javascript
// 从最安全的开始
function safeHookStrategy() {
    // 第一层：标准库
    hookStandardLibraries();
    
    // 第二层：WebView
    setTimeout(hookWebView, 3000);
    
    // 第三层：应用特定类（谨慎）
    setTimeout(hookAppSpecific, 8000);
}
```

### 2. 异常处理
```javascript
function safeHook(className, methodName) {
    try {
        var clazz = Java.use(className);
        clazz[methodName].implementation = function() {
            // Hook逻辑
            return this[methodName].apply(this, arguments);
        };
        console.log("[+] Hook成功: " + className + "." + methodName);
    } catch (e) {
        console.log("[-] Hook失败: " + className + "." + methodName + " - " + e);
    }
}
```

### 3. 内存管理
```javascript
// 限制数据存储
var maxRecords = 1000;
var records = [];

function addRecord(data) {
    records.push(data);
    if (records.length > maxRecords) {
        records = records.slice(-maxRecords);
    }
}
```

### 4. 延迟执行
```javascript
// 给应用足够的启动时间
Java.perform(function() {
    setTimeout(function() {
        // Hook代码
    }, 5000); // 延迟5秒
});
```

## 调试技巧

### 1. 逐步调试
```javascript
console.log("[DEBUG] 步骤1: 开始Hook");
// Hook代码
console.log("[DEBUG] 步骤1: 完成");
```

### 2. 类存在性检查
```javascript
function checkClassExists(className) {
    try {
        Java.use(className);
        console.log("[+] 类存在: " + className);
        return true;
    } catch (e) {
        console.log("[-] 类不存在: " + className);
        return false;
    }
}
```

### 3. 方法枚举
```javascript
function listMethods(className) {
    try {
        var clazz = Java.use(className);
        var methods = clazz.class.getDeclaredMethods();
        console.log("[*] " + className + " 的方法:");
        methods.forEach(function(method) {
            console.log("  - " + method.getName());
        });
    } catch (e) {
        console.log("[-] 无法枚举方法: " + e);
    }
}
```

## 微信版本兼容性

### 不同版本的差异
- **微信 8.0+**: 加强了反调试，需要更谨慎的Hook策略
- **微信 7.x**: 相对容易Hook，但类名可能不同
- **微信 6.x**: 较老版本，Hook方式可能完全不同

### 版本检测
```javascript
function getWeChatVersion() {
    try {
        var context = Java.use("android.app.ActivityThread").currentApplication().getApplicationContext();
        var packageManager = context.getPackageManager();
        var packageInfo = packageManager.getPackageInfo("com.tencent.mm", 0);
        console.log("[*] 微信版本: " + packageInfo.versionName.value);
        return packageInfo.versionName.value;
    } catch (e) {
        console.log("[-] 无法获取微信版本: " + e);
        return null;
    }
}
```

## 推荐的Hook顺序

1. **wx_request_minimal_hook.js** - 最安全，适合测试
2. **wx_request_safe_hook.js** - 平衡安全性和功能
3. **wx_request_advanced_hook.js** - 功能最全，但风险较高

根据您的需求和微信版本选择合适的脚本。
