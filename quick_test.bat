@echo off
chcp 65001 >nul
echo ========================================
echo 微信网络调试快速测试
echo ========================================
echo.

echo 请选择测试模式:
echo 1. 网络库发现模式 (推荐)
echo 2. 最小化Hook模式  
echo 3. 安全Hook模式
echo 4. 直接使用Frida命令
echo.
set /p choice=请选择 (1-4): 

if "%choice%"=="1" goto DISCOVERY
if "%choice%"=="2" goto MINIMAL  
if "%choice%"=="3" goto SAFE
if "%choice%"=="4" goto DIRECT
echo 无效选择
goto END

:DISCOVERY
echo.
echo [启动网络库发现模式]
echo 这个模式会帮助发现微信实际使用的网络库
echo.
python frida_debug_controller.py --mode discovery
goto END

:MINIMAL
echo.
echo [启动最小化Hook模式]
echo 使用最基础的Hook方法
echo.
python frida_debug_controller.py --mode minimal
goto END

:SAFE
echo.
echo [启动安全Hook模式]
echo 使用安全的Hook策略
echo.
python frida_debug_controller.py --mode safe
goto END

:DIRECT
echo.
echo [直接使用Frida命令]
echo 请选择要使用的脚本:
echo 1. wx_network_discovery.js
echo 2. wx_request_minimal_hook.js
echo 3. wx_request_safe_hook.js
echo.
set /p script_choice=请选择脚本 (1-3): 

if "%script_choice%"=="1" set script_name=wx_network_discovery.js
if "%script_choice%"=="2" set script_name=wx_request_minimal_hook.js
if "%script_choice%"=="3" set script_name=wx_request_safe_hook.js

echo.
echo 使用脚本: %script_name%
echo.
frida -U -f com.tencent.mm -l %script_name% --no-pause
goto END

:END
echo.
echo 测试完成
pause
