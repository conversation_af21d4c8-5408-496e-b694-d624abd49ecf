// 微信小程序网络请求拦截脚本
// 使用方法: frida -U -f com.tencent.mm -l wechat_miniprogram_hook.js --no-pause

console.log("[*] 开始Hook微信小程序网络请求...");

// Hook微信小程序的wx.request API
function hookWxRequest() {
    Java.perform(function() {
        try {
            // 方法1: Hook WebView中的XMLHttpRequest
            var WebView = Java.use("android.webkit.WebView");
            WebView.loadUrl.overload("java.lang.String").implementation = function(url) {
                console.log("[WebView] Loading URL: " + url);
                return this.loadUrl(url);
            };

            // 方法2: Hook OkHttp网络库 (微信常用)
            try {
                var OkHttpClient = Java.use("okhttp3.OkHttpClient");
                var Request = Java.use("okhttp3.Request");
                var Call = Java.use("okhttp3.Call");
                
                // Hook OkHttpClient的newCall方法
                OkHttpClient.newCall.implementation = function(request) {
                    console.log("[OkHttp] Request URL: " + request.url().toString());
                    console.log("[OkHttp] Request Method: " + request.method());
                    
                    // 打印请求头
                    var headers = request.headers();
                    console.log("[OkHttp] Headers: " + headers.toString());
                    
                    // 打印请求体
                    var requestBody = request.body();
                    if (requestBody != null) {
                        console.log("[OkHttp] Request Body: " + requestBody.toString());
                    }
                    
                    var call = this.newCall(request);
                    return call;
                };
            } catch (e) {
                console.log("[-] OkHttp hook failed: " + e);
            }

            // 方法3: Hook HttpURLConnection
            try {
                var HttpURLConnection = Java.use("java.net.HttpURLConnection");
                HttpURLConnection.getResponseCode.implementation = function() {
                    var url = this.getURL().toString();
                    console.log("[HttpURLConnection] Request URL: " + url);
                    console.log("[HttpURLConnection] Request Method: " + this.getRequestMethod());
                    
                    var result = this.getResponseCode();
                    console.log("[HttpURLConnection] Response Code: " + result);
                    return result;
                };
            } catch (e) {
                console.log("[-] HttpURLConnection hook failed: " + e);
            }

            // 方法4: Hook微信特定的网络类
            try {
                // 微信的网络请求类可能在不同版本中有所不同
                var classes = Java.enumerateLoadedClassesSync();
                classes.forEach(function(className) {
                    if (className.indexOf("tencent") !== -1 && 
                        (className.indexOf("network") !== -1 || 
                         className.indexOf("http") !== -1 || 
                         className.indexOf("request") !== -1)) {
                        console.log("[*] Found potential WeChat network class: " + className);
                        
                        try {
                            var clazz = Java.use(className);
                            var methods = clazz.class.getDeclaredMethods();
                            methods.forEach(function(method) {
                                var methodName = method.getName();
                                if (methodName.indexOf("request") !== -1 || 
                                    methodName.indexOf("send") !== -1 ||
                                    methodName.indexOf("post") !== -1 ||
                                    methodName.indexOf("get") !== -1) {
                                    console.log("[*] Found potential request method: " + className + "." + methodName);
                                }
                            });
                        } catch (e) {
                            // 忽略无法访问的类
                        }
                    }
                });
            } catch (e) {
                console.log("[-] WeChat class enumeration failed: " + e);
            }

            console.log("[+] 网络请求Hook设置完成");
            
        } catch (e) {
            console.log("[-] Hook失败: " + e);
        }
    });
}

// Hook JavaScript层面的网络请求
function hookJavaScriptRequests() {
    Java.perform(function() {
        try {
            // Hook WebView的evaluateJavascript方法来拦截JS执行
            var WebView = Java.use("android.webkit.WebView");
            WebView.evaluateJavascript.implementation = function(script, callback) {
                if (script.indexOf("wx.request") !== -1 || 
                    script.indexOf("XMLHttpRequest") !== -1 ||
                    script.indexOf("fetch") !== -1) {
                    console.log("[JS] Intercepted JavaScript: " + script);
                }
                return this.evaluateJavascript(script, callback);
            };

            // Hook WebView的loadUrl方法
            WebView.loadUrl.overload("java.lang.String", "java.util.Map").implementation = function(url, headers) {
                console.log("[WebView] Loading URL with headers: " + url);
                if (headers != null) {
                    console.log("[WebView] Headers: " + headers.toString());
                }
                return this.loadUrl(url, headers);
            };

        } catch (e) {
            console.log("[-] JavaScript hook failed: " + e);
        }
    });
}

// 启动Hook
setTimeout(function() {
    hookWxRequest();
    hookJavaScriptRequests();
}, 1000);

console.log("[*] 脚本加载完成，等待网络请求...");
