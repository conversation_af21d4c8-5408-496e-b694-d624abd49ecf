@echo off
chcp 65001 >nul
echo ========================================
echo 微信小程序网络流量拦截工具
echo Windows Frida + Android 设备
echo ========================================
echo.

:MENU
echo 请选择操作:
echo 1. 检查环境配置
echo 2. 启动基础拦截
echo 3. 启动高级拦截
echo 4. 使用Python控制脚本
echo 5. 查看帮助
echo 6. 退出
echo.
set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto CHECK_ENV
if "%choice%"=="2" goto BASIC_HOOK
if "%choice%"=="3" goto ADVANCED_HOOK
if "%choice%"=="4" goto PYTHON_CONTROLLER
if "%choice%"=="5" goto HELP
if "%choice%"=="6" goto EXIT
echo 无效选择，请重新输入
goto MENU

:CHECK_ENV
echo.
echo [检查环境配置]
echo.

echo 检查 Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python 未安装或未添加到PATH
) else (
    echo ✅ Python 已安装
)

echo 检查 Frida...
frida --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Frida 未安装，请运行: pip install frida-tools
) else (
    echo ✅ Frida 已安装
)

echo 检查 ADB...
adb version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ADB 未安装或未添加到PATH
) else (
    echo ✅ ADB 已安装
)

echo 检查设备连接...
adb devices | findstr "device" >nul
if %errorlevel% neq 0 (
    echo ❌ 未检测到Android设备
    echo 请确保:
    echo   - 设备已连接USB
    echo   - 已启用USB调试
    echo   - 已安装frida-server
) else (
    echo ✅ 检测到Android设备
)

echo.
pause
goto MENU

:BASIC_HOOK
echo.
echo [启动基础拦截]
echo 使用基础Hook脚本拦截微信网络请求...
echo 按 Ctrl+C 停止拦截
echo.
frida -U -f com.tencent.mm -l wechat_miniprogram_hook.js --no-pause
pause
goto MENU

:ADVANCED_HOOK
echo.
echo [启动高级拦截]
echo 使用高级Hook脚本进行深度拦截...
echo 按 Ctrl+C 停止拦截
echo.
frida -U -f com.tencent.mm -l wx_request_advanced_hook.js --no-pause
pause
goto MENU

:PYTHON_CONTROLLER
echo.
echo [Python控制脚本]
echo 启动Python控制器进行自动化拦截...
echo.
python frida_wechat_controller.py
pause
goto MENU

:HELP
echo.
echo [使用帮助]
echo.
echo 环境准备:
echo 1. 安装Python 3.7+
echo 2. 安装Frida: pip install frida-tools
echo 3. 安装ADB工具
echo 4. Android设备需要Root权限
echo 5. 在设备上安装并启动frida-server
echo.
echo 使用步骤:
echo 1. 连接Android设备到电脑
echo 2. 启用USB调试模式
echo 3. 在设备上启动frida-server
echo 4. 运行本脚本选择相应功能
echo.
echo 文件说明:
echo - wechat_miniprogram_hook.js: 基础拦截脚本
echo - wx_request_advanced_hook.js: 高级拦截脚本
echo - frida_wechat_controller.py: Python控制器
echo - WECHAT_FRIDA_GUIDE.md: 详细使用指南
echo.
pause
goto MENU

:EXIT
echo 感谢使用！
exit /b 0
